import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

from sklearn.preprocessing import MinMaxScaler

file_choose = "C:\\Users\\<USER>\\Downloads\\currency.csv"

data = pd.read_csv(file_choose)
print(len(data))

USD = data["USD-NT"]
data_all = np.array(USD).astype(float)
data_all = MinMaxScaler().fit_transform(data_all.reshape(-1, 1))
data_all = data_all.reshape(-1)
plt.plot(data_all)
plt.show()
time_step = 7
for i in range(len(data_all) - time_step):
    data.append(data_all[i:i + time_step+1], axis=1)

print(data.shape)
import numpy as np
import matplotlib.pyplot as plt
from dataclasses import dataclass, replace 
from typing import List, Tuple, Dict, Any
import itertools
from collections import defaultdict
import random

@dataclass
class ModelParameters:
    n_high_items: int = 3  # 高價值項目數量
    n_low_items: int = 3   # 低價值項目數量
    value_high: float = 8.0  # 高價值項目的價值
    value_low: float = 2.0   # 低價值項目的價值
    cost_offloading: float = 1  # 卸載一個項目的成本 (預設值，會在模擬Fig.7和後設認知模擬時改變)

    # 內部記憶準確率
    accuracy_internal_1_item: float = 0.95  # Fig.6 需要
    accuracy_internal_2_items: float = 0.925 # Fig.6 需要
    accuracy_internal_3_items: float = 0.90  # 標準情況: 只記一種類型 (3個)
    accuracy_internal_6_items: float = 0.75  # 標準情況: 記兩種類型 (6個)
    accuracy_offloaded: float = 0.98         # 外部卸載項目的準確率

    # 模擬控制參數
    n_episodes_per_strategy_eval: int = 20
    n_model_runs: int = 100 # 論文中為 1,000,000

    # 原有後設認知參數
    # 當代理人對內部記憶的信心低於此閾值時，如果策略允許，會傾向於卸載
    # 信心可以簡單地建模為理論內部記憶準確率 p_internal_actual
    metacognitive_threshold: float = 0.85  # 改為更接近內部記憶準確率的值

    # --- 新增: 擴展的後設認知參數 (基於 Hu, Luo, & Fleming 2019) ---
    # 元認知偏差: 0 表示信心與客觀準確率一致
    # >0 表示更自信 (主觀成功率 > 客觀成功率)
    # <0 表示更不自信 (主觀成功率 < 客觀成功率)
    metacognitive_bias: float = 0.0

    # 信心判斷的噪音標準差，用於模擬信心判斷的隨機性
    confidence_noise_std: float = 0.1  # 增加噪音以產生更多變異


# 策略表示: (store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed)
# 每個元素是布林值 (True/False)
# offload_*_allowed 現在表示該類型的項目是否可以被考慮卸載 (取決於後設認知和閾值)
Strategy = Tuple[bool, bool, bool, bool]

# Fig. 8 策略表示: (encode, offload_allowed)
StrategyFig8 = Tuple[bool, bool]


def get_all_strategies(allow_offloading = True, offload_restriction = "none") -> List[Strategy]:
    options_sh = [True, False] # 可以選擇是否記憶高價值
    options_sl = [True, False] # 可以選擇是否記憶低價值

    if not allow_offloading:
        options_oh = [False] # 不允許考慮卸載高價值
        options_ol = [False] # 不允許考慮卸載低價值
    else:
        if offload_restriction == "high_only":
            options_oh = [True, False]
            options_ol = [False] # 低價值不允許考慮卸載
        else: # "none" restriction or other unhandled
            options_oh = [True, False]
            options_ol = [True, False]
    return list(itertools.product(options_sh, options_sl, options_oh, options_ol))

def get_strategies_fig8() -> List[StrategyFig8]:
    # encode 可以是 True 或 False
    # offload_allowed 可以是 True 或 False
    return [
        (True, True),    # encode=True, offload_allowed=True
        (True, False),   # encode=True, offload_allowed=False
        (False, True),   # encode=False, offload_allowed=True
        (False, False)   # encode=False, offload_allowed=False
    ]


def get_internal_memory_accuracy(strategy_encoding_part: Tuple[bool, bool], params: ModelParameters) -> float:
    """
    根據策略的編碼部分計算理論內部記憶準確率 (作為信心基礎)
    strategy_encoding_part: (store_H_internal, store_L_internal)
    """
    items_stored_internally = 0
    if strategy_encoding_part[0]: # 選擇記憶高價值
        items_stored_internally += params.n_high_items
    if strategy_encoding_part[1]: # 選擇記憶低價值
        items_stored_internally += params.n_low_items

    if items_stored_internally == 0:
        return 0.0
    # 邏輯是根據總內部項目數來決定準確率曲線
    elif items_stored_internally == 1:
        return params.accuracy_internal_1_item
    elif items_stored_internally == 2:
        return params.accuracy_internal_2_items
    elif items_stored_internally == 3:
        return params.accuracy_internal_3_items
    elif items_stored_internally == (params.n_high_items + params.n_low_items):
         return params.accuracy_internal_6_items
    else:
        # 如果項目數不是預設的 1, 2, 3, 6，提供一個簡單的 fallback 或根據需要調整
        # print(f"Warning: items_stored_internally count ({items_stored_internally}) not specifically defined in ModelParameters accuracies.")
        # 使用最低定義的準確率作為簡單回退（如果大於0）
        if items_stored_internally > 0:
            return params.accuracy_internal_6_items
        else:
            return 0.0 # 0 items means 0 accuracy


def get_perceived_internal_success_rate(objective_internal_accuracy: float, params: ModelParameters) -> float:
    # 基礎感知成功率 = 客觀準確率 + 後設認知偏差
    perceived_rate = objective_internal_accuracy + params.metacognitive_bias

    # 加入噪音，模擬信心判斷的隨機性
    if params.confidence_noise_std > 0:
        perceived_rate += np.random.normal(0, params.confidence_noise_std)

    # 確保感知成功率在 0 和 1 之間
    return np.clip(perceived_rate, 0, 1)


def calculate_accuracy_for_strategy(strategy: Tuple[bool, bool, bool, bool], params: ModelParameters, mode: str = "normal") -> Tuple[float, float]:
    """
    計算一個策略的理論客觀準確率 (不進行實際模擬)

    Args:
        strategy: (store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed)
        params: 模型參數
        mode: "normal" 或 "surprise_test"，surprise_test 只考慮內部記憶

    Returns:
        (高價值準確率, 低價值準確率)
    """
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy

    # 獲取客觀的內部記憶準確率
    objective_p_internal = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)

    # 計算高價值項目的準確率
    if mode == "normal":
        # 正常測試：考慮內部記憶和卸載
        if store_H_internal and offload_H_allowed:
            # 如果同時使用內部記憶和卸載，成功率是兩者的聯合概率
            # P(成功) = 1 - P(兩者都失敗) = 1 - (1-P_int)(1-P_ext)
            acc_H = 1.0 - (1.0 - objective_p_internal) * (1.0 - params.accuracy_offloaded)
        elif store_H_internal:
            # 只使用內部記憶
            acc_H = objective_p_internal
        elif offload_H_allowed:
            # 只使用卸載
            acc_H = params.accuracy_offloaded
        else:
            # 既不使用內部記憶也不使用卸載
            acc_H = 0.0
    else:  # "surprise_test"
        # 意外測試：只考慮內部記憶
        acc_H = objective_p_internal if store_H_internal else 0.0

    # 計算低價值項目的準確率 (邏輯同上)
    if mode == "normal":
        if store_L_internal and offload_L_allowed:
            acc_L = 1.0 - (1.0 - objective_p_internal) * (1.0 - params.accuracy_offloaded)
        elif store_L_internal:
            acc_L = objective_p_internal
        elif offload_L_allowed:
            acc_L = params.accuracy_offloaded
        else:
            acc_L = 0.0
    else:  # "surprise_test"
        acc_L = objective_p_internal if store_L_internal else 0.0

    return acc_H, acc_L


def evaluate_strategy_subjective(strategy: Tuple[bool, bool, bool, bool], params: ModelParameters) -> float:
    """
    評估一個策略在多次試驗中的「主觀感知」平均獎勵。
    基於 Hu, Luo, & Fleming (2019) 的理論，考慮後設認知偏差對策略評估的影響。

    Args:
        strategy: (store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed)
        params: 模型參數

    Returns:
        策略的主觀預期獎勵
    """
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy

    # 獲取客觀的內部記憶準確率
    objective_p_internal_actual = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)

    # 進行多次信心取樣，計算平均主觀預期獎勵
    rewards_this_strategy_subjective = []

    for _ in range(params.n_episodes_per_strategy_eval):
        # 獲取主觀感知的內部記憶成功率 (受後設認知偏差和噪音影響)
        perceived_p_internal = get_perceived_internal_success_rate(objective_p_internal_actual, params)

        current_subjective_expected_reward = 0.0

        # --- 高價值項目 ---
        cost_H_offload = params.n_high_items * params.cost_offloading if offload_H_allowed else 0.0

        if store_H_internal and offload_H_allowed:
            # 同時使用內部記憶和卸載
            # 主觀成功率 = 1 - (1-主觀內部成功率)(1-客觀外部成功率)
            subjective_prob_remember_H = 1.0 - (1.0 - perceived_p_internal) * (1.0 - params.accuracy_offloaded)
            current_subjective_expected_reward += params.n_high_items * params.value_high * subjective_prob_remember_H
            current_subjective_expected_reward -= cost_H_offload  # 卸載成本照算
        elif store_H_internal:
            # 只使用內部記憶
            current_subjective_expected_reward += params.n_high_items * params.value_high * perceived_p_internal
        elif offload_H_allowed:
            # 只使用卸載
            current_subjective_expected_reward += params.n_high_items * params.value_high * params.accuracy_offloaded
            current_subjective_expected_reward -= cost_H_offload

        # --- 低價值項目 ---
        cost_L_offload = params.n_low_items * params.cost_offloading if offload_L_allowed else 0.0

        if store_L_internal and offload_L_allowed:
            # 同時使用內部記憶和卸載
            subjective_prob_remember_L = 1.0 - (1.0 - perceived_p_internal) * (1.0 - params.accuracy_offloaded)
            current_subjective_expected_reward += params.n_low_items * params.value_low * subjective_prob_remember_L
            current_subjective_expected_reward -= cost_L_offload
        elif store_L_internal:
            # 只使用內部記憶
            current_subjective_expected_reward += params.n_low_items * params.value_low * perceived_p_internal
        elif offload_L_allowed:
            # 只使用卸載
            current_subjective_expected_reward += params.n_low_items * params.value_low * params.accuracy_offloaded
            current_subjective_expected_reward -= cost_L_offload

        rewards_this_strategy_subjective.append(current_subjective_expected_reward)

    # 返回平均主觀預期獎勵
    return np.mean(rewards_this_strategy_subjective)


# 修改 simulate_trial 函數以包含後設認知和每個項目的卸載決策
def simulate_trial(params: ModelParameters, strategy: Strategy) -> Tuple[float, int, int, int, int, int, int]:
    """
    模擬單次試驗並計算總獎勵、命中數和實際卸載數。
    返回: (總獎勵, 高價值正常命中數, 低價值正常命中數, 高價值意外命中數, 低價值意外命中數, 實際卸載高價值數, 實際卸載低價值數)
    """    # 初始化
    total_reward = 0.0
    high_value_hits_normal_count = 0
    low_value_hits_normal_count = 0
    high_value_hits_surprise_count = 0
    low_value_hits_surprise_count = 0
    actual_offloaded_H_count = 0 # 實際卸載的高價值項目數
    actual_offloaded_L_count = 0 # 實際卸載的低價值項目數

    # 調試變數
    metacognitive_decisions_H = 0
    metacognitive_decisions_L = 0
    
    # 獲取內部記憶的理論準確率
    p_internal_actual = get_internal_memory_accuracy((strategy[0], strategy[1]), params)

    # 處理高價值項目
    confidence_H_basis = p_internal_actual if strategy[0] else 0.0

    # 高價值項目後設認知決策
    for _ in range(params.n_high_items):
        decided_to_offload_this_H_item = False
        if strategy[2]:  # 如果策略允許卸載高價值
            perceived_confidence = get_perceived_internal_success_rate(confidence_H_basis, params)
            if perceived_confidence < params.metacognitive_threshold:
                decided_to_offload_this_H_item = True
                metacognitive_decisions_H += 1
        
        # 根據決策模擬回憶結果
        item_recalled_H_normal = False
        item_recalled_H_surprise = False # 意外測試只考慮內部記憶

        if decided_to_offload_this_H_item:
            actual_offloaded_H_count += 1
            # 從外部記憶回憶
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_H_normal = True
            # 從外部記憶回憶成功，不算意外命中
        else:
            # 依賴內部記憶 (只有在策略允許編碼高價值時才可能)
            if strategy[0]:
                 # 內部記憶是否成功取決於實際的内部準確率
                 if np.random.rand() < p_internal_actual: # 使用 p_internal_actual 模擬實際內部回憶
                     item_recalled_H_normal = True # 內部回憶成功也是正常命中
                     item_recalled_H_surprise = True # 內部記憶成功才算意外命中


        if item_recalled_H_normal:
            high_value_hits_normal_count += 1
            total_reward += params.value_high # 正常回憶成功獲得獎勵

        if item_recalled_H_surprise:
             high_value_hits_surprise_count += 1
    ### 處理低價值項目 (邏輯同高價值項目)
    confidence_L_basis = p_internal_actual if strategy[1] else 0.0

    # 低價值項目後設認知決策 - 修正為使用相同邏輯
    for _ in range(params.n_low_items):
        decided_to_offload_this_L_item = False        # 修正後設認知決策邏輯 - 與高價值項目保持一致
        if strategy[3]:  # 如果策略允許卸載低價值
            # 使用感知的成功率而非理論準確率
            perceived_confidence_L = get_perceived_internal_success_rate(confidence_L_basis, params)
            if perceived_confidence_L < params.metacognitive_threshold:
                decided_to_offload_this_L_item = True
                metacognitive_decisions_L += 1  # 計數後設認知決策次數

        # 根據決策模擬回憶結果
        item_recalled_L_normal = False
        item_recalled_L_surprise = False # 意外測試只考慮內部記憶

        if decided_to_offload_this_L_item:
            actual_offloaded_L_count += 1
            # 從外部記憶回憶
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_L_normal = True
            # 從外部記憶回憶成功，不算意外命中
        else:
            # 依賴內部記憶 (只有在策略允許編碼低價值時才可能)
            if strategy[1]:
                 # 內部記憶是否成功取決於實際的内部準確率
                 if np.random.rand() < p_internal_actual: # 使用 p_internal_actual 模擬實際內部回憶
                     item_recalled_L_normal = True # 內部回憶成功也是正常命中
                     item_recalled_L_surprise = True # 內部記憶成功才算意外命中


        if item_recalled_L_normal:
            low_value_hits_normal_count += 1
            total_reward += params.value_low # 正常回憶成功獲得獎勵

        if item_recalled_L_surprise:
             low_value_hits_surprise_count += 1    # 計算總卸載成本 (根據實際卸載的項目數)
    total_reward -= actual_offloaded_H_count * params.cost_offloading
    total_reward -= actual_offloaded_L_count * params.cost_offloading    # 調試輸出 (僅在有後設認知決策時輸出，並限制頻率)
    if (metacognitive_decisions_H > 0 or metacognitive_decisions_L > 0) and np.random.random() < 0.1:
        print(f"  後設認知決策: 高價值={metacognitive_decisions_H}/{params.n_high_items}, 低價值={metacognitive_decisions_L}/{params.n_low_items}, 閾值={params.metacognitive_threshold:.2f}")

    return total_reward, high_value_hits_normal_count, low_value_hits_normal_count, high_value_hits_surprise_count, low_value_hits_surprise_count, actual_offloaded_H_count, actual_offloaded_L_count


# 原始的 simulate_trial 函數 (不包含後設認知，用於 Fig 2-8)
def simulate_trial_original(params: ModelParameters, strategy: Strategy) -> Tuple[float, int, int, int, int, int, int]:
    """
    模擬單次試驗並計算總獎勵、命中數 (原始版本，不包含後設認知)
    返回: (總獎勵, 高價值正常命中數, 低價值正常命中數, 高價值意外命中數, 低價值意外命中數, 實際卸載高價值數, 實際卸載低價值數)
    """
    store_H_internal, store_L_internal, offload_H_allowed, offload_L_allowed = strategy
    
    total_reward = 0.0
    high_value_hits_normal_count = 0
    low_value_hits_normal_count = 0
    high_value_hits_surprise_count = 0
    low_value_hits_surprise_count = 0
    actual_offloaded_H_count = 0
    actual_offloaded_L_count = 0

    # 計算內部記憶準確率
    p_internal_actual = get_internal_memory_accuracy((store_H_internal, store_L_internal), params)

    # 處理高價值項目
    for _ in range(params.n_high_items):
        item_recalled_H_normal = False
        item_recalled_H_surprise = False

        if store_H_internal and offload_H_allowed:
            # 同時使用內部記憶和卸載
            actual_offloaded_H_count += 1
            # 聯合概率: 1 - (1-p_internal)(1-p_external)
            combined_success_prob = 1.0 - (1.0 - p_internal_actual) * (1.0 - params.accuracy_offloaded)
            if np.random.rand() < combined_success_prob:
                item_recalled_H_normal = True
            # 意外測試只考慮內部記憶
            if np.random.rand() < p_internal_actual:
                item_recalled_H_surprise = True
        elif store_H_internal:
            # 只使用內部記憶
            if np.random.rand() < p_internal_actual:
                item_recalled_H_normal = True
                item_recalled_H_surprise = True
        elif offload_H_allowed:
            # 只使用卸載
            actual_offloaded_H_count += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_H_normal = True

        if item_recalled_H_normal:
            high_value_hits_normal_count += 1
            total_reward += params.value_high

        if item_recalled_H_surprise:
            high_value_hits_surprise_count += 1

    # 處理低價值項目
    for _ in range(params.n_low_items):
        item_recalled_L_normal = False
        item_recalled_L_surprise = False

        if store_L_internal and offload_L_allowed:
            # 同時使用內部記憶和卸載
            actual_offloaded_L_count += 1
            combined_success_prob = 1.0 - (1.0 - p_internal_actual) * (1.0 - params.accuracy_offloaded)
            if np.random.rand() < combined_success_prob:
                item_recalled_L_normal = True
            if np.random.rand() < p_internal_actual:
                item_recalled_L_surprise = True
        elif store_L_internal:
            # 只使用內部記憶
            if np.random.rand() < p_internal_actual:
                item_recalled_L_normal = True
                item_recalled_L_surprise = True
        elif offload_L_allowed:
            # 只使用卸載
            actual_offloaded_L_count += 1
            if np.random.rand() < params.accuracy_offloaded:
                item_recalled_L_normal = True

        if item_recalled_L_normal:
            low_value_hits_normal_count += 1
            total_reward += params.value_low

        if item_recalled_L_surprise:
            low_value_hits_surprise_count += 1

    # 計算卸載成本
    total_reward -= actual_offloaded_H_count * params.cost_offloading
    total_reward -= actual_offloaded_L_count * params.cost_offloading

    return total_reward, high_value_hits_normal_count, low_value_hits_normal_count, high_value_hits_surprise_count, low_value_hits_surprise_count, actual_offloaded_H_count, actual_offloaded_L_count


# 原始的 run_simulation 函數 (不包含後設認知，用於 Fig 2-8)
def run_simulation_original(params: ModelParameters, strategies: List[Strategy]) -> Dict[str, Any]:
    print(f"評估運行模擬 (原始方法) ({params.n_model_runs} runs, {params.n_episodes_per_strategy_eval} episodes per run)...")

    overall_best_strategy_counts: Dict[Strategy, int] = {s: 0 for s in strategies}
    cumulative_best_strategy_total_hits_H_normal = 0
    cumulative_best_strategy_total_hits_L_normal = 0
    cumulative_best_strategy_total_hits_H_surprise = 0
    cumulative_best_strategy_total_hits_L_surprise = 0
    cumulative_best_strategy_total_offloaded_H = 0
    cumulative_best_strategy_total_offloaded_L = 0
    cumulative_best_strategy_total_episodes = 0
    successful_runs_count = 0

    for i_run in range(params.n_model_runs):
        if (i_run + 1) % (max(1, params.n_model_runs // 100)) == 0:
            print(f"  運行中 (原始)... {((i_run + 1) / params.n_model_runs) * 100:.0f}%")

        total_reward_per_strategy_this_run: Dict[Strategy, float] = defaultdict(float)
        sample_count_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_offloaded_H_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_offloaded_L_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)

        allowed_strategies_list_this_run = strategies

        for i_episode in range(params.n_episodes_per_strategy_eval):
            chosen_strategy = random.choice(allowed_strategies_list_this_run)
            chosen_strategy_tuple = tuple(chosen_strategy)

            # 使用原始的 simulate_trial
            reward, hits_H_norm, hits_L_norm, hits_H_surp, hits_L_surp, actual_offloaded_H, actual_offloaded_L = simulate_trial_original(params, chosen_strategy_tuple)

            total_reward_per_strategy_this_run[chosen_strategy_tuple] += reward
            sample_count_per_strategy_this_run[chosen_strategy_tuple] += 1
            total_hits_H_normal_per_strategy_this_run[chosen_strategy_tuple] += hits_H_norm
            total_hits_L_normal_per_strategy_this_run[chosen_strategy_tuple] += hits_L_norm
            total_hits_H_surprise_per_strategy_this_run[chosen_strategy_tuple] += hits_H_surp
            total_hits_L_surprise_per_strategy_this_run[chosen_strategy_tuple] += hits_L_surp
            total_offloaded_H_per_strategy_this_run[chosen_strategy_tuple] += actual_offloaded_H
            total_offloaded_L_per_strategy_this_run[chosen_strategy_tuple] += actual_offloaded_L

        mean_rewards_this_run: Dict[Strategy, float] = {}
        max_avg_reward_this_run = -float('inf')
        strategies_sampled_this_run = [s for s in strategies if sample_count_per_strategy_this_run[s] > 0]

        if not strategies_sampled_this_run:
            continue

        for strategy in strategies_sampled_this_run:
            if sample_count_per_strategy_this_run[strategy] > 0:
                mean_reward = total_reward_per_strategy_this_run[strategy] / sample_count_per_strategy_this_run[strategy]
                mean_rewards_this_run[strategy] = mean_reward
                if mean_reward > max_avg_reward_this_run:
                    max_avg_reward_this_run = mean_reward

        tolerance = 1e-9
        best_strategies_this_run = [
            s for s, avg_r in mean_rewards_this_run.items() if abs(avg_r - max_avg_reward_this_run) < tolerance
        ]

        best_strategy_for_this_run = random.choice(best_strategies_this_run)
        overall_best_strategy_counts[best_strategy_for_this_run] += 1

        samples_best_strategy_this_run = sample_count_per_strategy_this_run[best_strategy_for_this_run]

        if samples_best_strategy_this_run > 0:
            cumulative_best_strategy_total_hits_H_normal += total_hits_H_normal_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_L_normal += total_hits_L_normal_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_H_surprise += total_hits_H_surprise_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_L_surprise += total_hits_L_surprise_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_offloaded_H += total_offloaded_H_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_offloaded_L += total_offloaded_L_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_episodes += samples_best_strategy_this_run
            successful_runs_count += 1

    total_item_opportunities_H = cumulative_best_strategy_total_episodes * params.n_high_items
    total_item_opportunities_L = cumulative_best_strategy_total_episodes * params.n_low_items

    mean_empirical_accuracy_H_normal = cumulative_best_strategy_total_hits_H_normal / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_accuracy_L_normal = cumulative_best_strategy_total_hits_L_normal / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0
    mean_empirical_accuracy_H_surprise = cumulative_best_strategy_total_hits_H_surprise / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_accuracy_L_surprise = cumulative_best_strategy_total_hits_L_surprise / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0
    mean_empirical_offload_H = cumulative_best_strategy_total_offloaded_H / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_offload_L = cumulative_best_strategy_total_offloaded_L / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0

    strategy_proportions: Dict[Strategy, float] = {
        s: overall_best_strategy_counts[s] / successful_runs_count if successful_runs_count > 0 else 0.0 for s in strategies
    }

    print("\n最終策略選擇比例:")
    sorted_overall_proportions = sorted(strategy_proportions.items(), key=lambda item: item[1], reverse=True)
    for s, prop in sorted_overall_proportions:
        print(f"  策略 {s}: 比例 {prop:.3f}")

    print(f"\n平均經驗命中率和卸載率:")
    print(f"  正常準確率 - 高價值: {mean_empirical_accuracy_H_normal:.3f}")
    print(f"  正常準確率 - 低價值: {mean_empirical_accuracy_L_normal:.3f}")
    print(f"  意外準確率 - 高價值: {mean_empirical_accuracy_H_surprise:.3f}")
    print(f"  意外準確率 - 低價值: {mean_empirical_accuracy_L_surprise:.3f}")
    print(f"  卸載率 - 高價值: {mean_empirical_offload_H:.3f}")
    print(f"  卸載率 - 低價值: {mean_empirical_offload_L:.3f}")

    return {
        "strategy_proportions": strategy_proportions,
        "mean_accuracy_H": mean_empirical_accuracy_H_normal,
        "mean_accuracy_L": mean_empirical_accuracy_L_normal,
        "mean_accuracy_H_surprise": mean_empirical_accuracy_H_surprise,
        "mean_accuracy_L_surprise": mean_empirical_accuracy_L_surprise,
        "mean_offload_H": mean_empirical_offload_H,
        "mean_offload_L": mean_empirical_offload_L,
        "overall_best_strategy_counts": overall_best_strategy_counts
    }

# 新增後設認知模擬的 run_simulation 函數
def run_simulation_with_metacognition(params: ModelParameters, strategies: List[Strategy]) -> Dict[str, Any]:
    """
    運行包含後設認知的模擬
    """
    print(f"評估運行模擬 (包含後設認知) ({params.n_model_runs} runs, {params.n_episodes_per_strategy_eval} episodes per run)...")

    overall_best_strategy_counts: Dict[Strategy, int] = {s: 0 for s in strategies}
    cumulative_best_strategy_total_hits_H_normal = 0
    cumulative_best_strategy_total_hits_L_normal = 0
    cumulative_best_strategy_total_hits_H_surprise = 0
    cumulative_best_strategy_total_hits_L_surprise = 0
    cumulative_best_strategy_total_offloaded_H = 0
    cumulative_best_strategy_total_offloaded_L = 0
    cumulative_best_strategy_total_episodes = 0
    successful_runs_count = 0

    for i_run in range(params.n_model_runs):
        if (i_run + 1) % (max(1, params.n_model_runs // 100)) == 0:
            print(f"  運行中 (後設認知)... {((i_run + 1) / params.n_model_runs) * 100:.0f}%")

        total_reward_per_strategy_this_run: Dict[Strategy, float] = defaultdict(float)
        sample_count_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_normal_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_H_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_hits_L_surprise_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_offloaded_H_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)
        total_offloaded_L_per_strategy_this_run: Dict[Strategy, int] = defaultdict(int)

        allowed_strategies_list_this_run = strategies

        # 在每個 episode 中，應該基於主觀評估選擇策略
        for i_episode in range(params.n_episodes_per_strategy_eval):
            # 計算每個策略的主觀預期獎勵
            strategy_subjective_rewards = {}
            for strategy in allowed_strategies_list_this_run:
                strategy_subjective_rewards[strategy] = evaluate_strategy_subjective(strategy, params)
            
            # 選擇主觀預期獎勵最高的策略
            best_subjective_strategy = max(strategy_subjective_rewards.keys(), 
                                         key=lambda s: strategy_subjective_rewards[s])
            chosen_strategy_tuple = tuple(best_subjective_strategy)
            
            # 使用包含後設認知的 simulate_trial
            reward, hits_H_norm, hits_L_norm, hits_H_surp, hits_L_surp, actual_offloaded_H, actual_offloaded_L = simulate_trial(params, chosen_strategy_tuple)

            total_reward_per_strategy_this_run[chosen_strategy_tuple] += reward
            sample_count_per_strategy_this_run[chosen_strategy_tuple] += 1
            total_hits_H_normal_per_strategy_this_run[chosen_strategy_tuple] += hits_H_norm
            total_hits_L_normal_per_strategy_this_run[chosen_strategy_tuple] += hits_L_norm
            total_hits_H_surprise_per_strategy_this_run[chosen_strategy_tuple] += hits_H_surp
            total_hits_L_surprise_per_strategy_this_run[chosen_strategy_tuple] += hits_L_surp
            total_offloaded_H_per_strategy_this_run[chosen_strategy_tuple] += actual_offloaded_H
            total_offloaded_L_per_strategy_this_run[chosen_strategy_tuple] += actual_offloaded_L

        mean_rewards_this_run: Dict[Strategy, float] = {}
        max_avg_reward_this_run = -float('inf')
        strategies_sampled_this_run = [s for s in strategies if sample_count_per_strategy_this_run[s] > 0]

        if not strategies_sampled_this_run:
            continue

        for strategy in strategies_sampled_this_run:
            if sample_count_per_strategy_this_run[strategy] > 0:
                mean_reward = total_reward_per_strategy_this_run[strategy] / sample_count_per_strategy_this_run[strategy]
                mean_rewards_this_run[strategy] = mean_reward
                if mean_reward > max_avg_reward_this_run:
                    max_avg_reward_this_run = mean_reward

        tolerance = 1e-9
        best_strategies_this_run = [
            s for s, avg_r in mean_rewards_this_run.items() if abs(avg_r - max_avg_reward_this_run) < tolerance
        ]

        best_strategy_for_this_run = random.choice(best_strategies_this_run)
        overall_best_strategy_counts[best_strategy_for_this_run] += 1

        samples_best_strategy_this_run = sample_count_per_strategy_this_run[best_strategy_for_this_run]

        if samples_best_strategy_this_run > 0:
            cumulative_best_strategy_total_hits_H_normal += total_hits_H_normal_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_L_normal += total_hits_L_normal_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_H_surprise += total_hits_H_surprise_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_hits_L_surprise += total_hits_L_surprise_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_offloaded_H += total_offloaded_H_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_offloaded_L += total_offloaded_L_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_episodes += samples_best_strategy_this_run
            successful_runs_count += 1

    total_item_opportunities_H = cumulative_best_strategy_total_episodes * params.n_high_items
    total_item_opportunities_L = cumulative_best_strategy_total_episodes * params.n_low_items

    mean_empirical_accuracy_H_normal = cumulative_best_strategy_total_hits_H_normal / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_accuracy_L_normal = cumulative_best_strategy_total_hits_L_normal / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0
    mean_empirical_accuracy_H_surprise = cumulative_best_strategy_total_hits_H_surprise / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_accuracy_L_surprise = cumulative_best_strategy_total_hits_L_surprise / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0
    mean_empirical_offload_H = cumulative_best_strategy_total_offloaded_H / total_item_opportunities_H if total_item_opportunities_H > 0 else 0.0
    mean_empirical_offload_L = cumulative_best_strategy_total_offloaded_L / total_item_opportunities_L if total_item_opportunities_L > 0 else 0.0

    strategy_proportions: Dict[Strategy, float] = {
        s: overall_best_strategy_counts[s] / successful_runs_count if successful_runs_count > 0 else 0.0 for s in strategies
    }

    print("\n最終策略選擇比例 (後設認知):")
    sorted_overall_proportions = sorted(strategy_proportions.items(), key=lambda item: item[1], reverse=True)
    for s, prop in sorted_overall_proportions:
        print(f"  策略 {s}: 比例 {prop:.3f}")

    print(f"\n平均經驗命中率和卸載率 (後設認知):")
    print(f"  正常準確率 - 高價值: {mean_empirical_accuracy_H_normal:.3f}")
    print(f"  正常準確率 - 低價值: {mean_empirical_accuracy_L_normal:.3f}")
    print(f"  意外準確率 - 高價值: {mean_empirical_accuracy_H_surprise:.3f}")
    print(f"  意外準確率 - 低價值: {mean_empirical_accuracy_L_surprise:.3f}")
    print(f"  卸載率 - 高價值: {mean_empirical_offload_H:.3f}")
    print(f"  卸載率 - 低價值: {mean_empirical_offload_L:.3f}")

    return {
        "strategy_proportions": strategy_proportions,
        "mean_accuracy_H": mean_empirical_accuracy_H_normal,
        "mean_accuracy_L": mean_empirical_accuracy_L_normal,
        "mean_accuracy_H_surprise": mean_empirical_accuracy_H_surprise,
        "mean_accuracy_L_surprise": mean_empirical_accuracy_L_surprise,
        "mean_offload_H": mean_empirical_offload_H,
        "mean_offload_L": mean_empirical_offload_L,
        "overall_best_strategy_counts": overall_best_strategy_counts
    }

# --- 模擬 Fig. 2: 不允許卸載 ---
def simulate_fig2(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 2 (不允許考慮卸載)...")
    params_fig2 = replace(base_params,
        n_high_items=3, n_low_items=3,
    )
    # Fig.2 不允許【考慮】卸載
    strategies_fig2 = get_all_strategies(allow_offloading=False)
    # 使用原始的 run_simulation (不包含後設認知)
    results = run_simulation_original(params_fig2, strategies_fig2)

    # 匯總和打印結果
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]

    print(f"Fig. 2 結果 (不允許考慮卸載): ")
    print(f"  策略 - 編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略 - 編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  正常準確率 - 高價值: {acc_H_norm:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4))
    fig.suptitle(f"Fig. 2 Simulation: No Offloading Allowed", fontsize=14)
    axs[0].bar(["Encode\nlow-value", "Encode\nhigh-value"], [prop_encode_low, prop_encode_high], color='cornflowerblue')
    axs[0].set_title("Strategy ")
    axs[0].set_ylabel("Proportion of Runs Choosing Strategy")
    axs[0].set_ylim(0, 1.05)
    axs[1].bar(["Low-value", "High-value"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("Mean Empirical Normal Accuracy ")
    axs[1].set_ylabel("Accuracy")
    axs[1].set_ylim(0, 1.05)
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return results

# --- 模擬 Fig. 3: 允許卸載 ---
def simulate_fig3(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 3 (允許考慮卸載)...")
    params_fig3 = replace(base_params,
        n_high_items=3, n_low_items=3,
    )
    # Fig.3 允許所有【考慮】卸載策略
    strategies_fig3 = get_all_strategies(allow_offloading=True)

    # 使用原始的 run_simulation 函數
    results = run_simulation_original(params_fig3, strategies_fig3)

    # 匯總和打印結果
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    prop_offload_low_allowed = sum(prop for s, prop in results["strategy_proportions"].items() if s[3])
    prop_offload_high_allowed = sum(prop for s, prop in results["strategy_proportions"].items() if s[2])

    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]
    offload_L_rate = results["mean_offload_L"]
    offload_H_rate = results["mean_offload_H"]

    print(f"Fig. 3 結果 (允許考慮卸載): ")
    print(f"  策略中允許編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略中允許編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  策略中允許卸載低價值比例: {prop_offload_low_allowed:.3f}")
    print(f"  策略中允許卸載高價值比例: {prop_offload_high_allowed:.3f}")
    print(f"  正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  正常準確率 - 高價值: {acc_H_norm:.3f}")
    print(f"  平均經驗卸載率 - 低價值: {offload_L_rate:.3f}")
    print(f"  平均經驗卸載率 - 高價值: {offload_H_rate:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 3 Simulation: Offloading Allowed", fontsize=14)

    strategy_labels = ["Encode\nlow-value", "Encode\nhigh-value", "Allow Offload\nlow-value", "Allow Offload\nhigh-value"]
    strategy_values = [prop_encode_low, prop_encode_high, prop_offload_low_allowed, prop_offload_high_allowed]
    axs[0].bar(strategy_labels, strategy_values, color='cornflowerblue')
    axs[0].set_title("Strategy Choice Proportion")
    axs[0].set_ylabel("Proportion of Runs Choosing Strategy")
    axs[0].set_ylim(0, 1.05)
    plt.setp(axs[0].get_xticklabels(), rotation=15, ha="right")

    axs[1].bar(["Low-value", "High-value"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("Mean Empirical Normal Accuracy")
    axs[1].set_ylabel("Accuracy")
    axs[1].set_ylim(0, 1.05)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    # 新增繪圖：平均經驗卸載率
    fig, ax = plt.subplots(figsize=(8, 6))
    fig.suptitle(f"Fig. 3 Simulation: Mean Empirical Offloading Rate", fontsize=14)
    ax.bar(["Low-value", "High-value"], [offload_L_rate, offload_H_rate], color=['cornflowerblue', 'brown'])
    ax.set_title("Mean Empirical Offloading Rate")
    ax.set_ylabel("Offloading Rate")
    ax.set_ylim(0, 1.05)
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return results

# --- 模擬 Fig. 4: 只允許卸載高價值項目 ---
def simulate_fig4(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 4 (只允許考慮卸載高價值項目)...")
    params_fig4 = replace(base_params,
        n_high_items=3, n_low_items=3,
    )
    # 策略: 允許考慮卸載，但僅限高價值
    strategies_fig4 = get_all_strategies(allow_offloading=True, offload_restriction="high_only")

    # 使用原始的 run_simulation 函數
    results = run_simulation_original(params_fig4, strategies_fig4)

    # 匯總和打印結果
    prop_encode_low = sum(prop for s, prop in results["strategy_proportions"].items() if s[1])
    prop_encode_high = sum(prop for s, prop in results["strategy_proportions"].items() if s[0])
    prop_offload_low_allowed_check = sum(prop for s, prop in results["strategy_proportions"].items() if s[3])
    prop_offload_high_allowed = sum(prop for s, prop in results["strategy_proportions"].items() if s[2])

    acc_L_norm = results["mean_accuracy_L"]
    acc_H_norm = results["mean_accuracy_H"]
    offload_L_rate = results["mean_offload_L"]
    offload_H_rate = results["mean_offload_H"]

    print(f"Fig. 4 結果 (只允許考慮卸載高價值): ")
    print(f"  策略中允許編碼低價值比例: {prop_encode_low:.3f}")
    print(f"  策略中允許編碼高價值比例: {prop_encode_high:.3f}")
    print(f"  策略中允許卸載低價值比例 (應為0): {prop_offload_low_allowed_check:.3f}")
    print(f"  策略中允許卸載高價值比例: {prop_offload_high_allowed:.3f}")
    print(f"  正常準確率 - 低價值: {acc_L_norm:.3f}")
    print(f"  正常準確率 - 高價值: {acc_H_norm:.3f}")
    print(f"  平均經驗卸載率 - 低價值: {offload_L_rate:.3f}")
    print(f"  平均經驗卸載率 - 高價值: {offload_H_rate:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 4 Simulation: Only High-Value Offloading Allowed", fontsize=14)

    strategy_labels = ["Encode\nlow-value", "Encode\nhigh-value", "Allow Offload\nlow-value", "Allow Offload\nhigh-value"]
    strategy_values = [prop_encode_low, prop_encode_high, prop_offload_low_allowed_check, prop_offload_high_allowed]
    axs[0].bar(strategy_labels, strategy_values, color='cornflowerblue')
    axs[0].set_title("Strategy Choice Proportion")
    axs[0].set_ylabel("Proportion of Runs Choosing Strategy")
    axs[0].set_ylim(0, 1.05)
    plt.setp(axs[0].get_xticklabels(), rotation=15, ha="right")

    axs[1].bar(["Low-value", "High-value"], [acc_L_norm, acc_H_norm], color='cornflowerblue')
    axs[1].set_title("Mean Empirical Normal Accuracy")
    axs[1].set_ylabel("Accuracy")
    axs[1].set_ylim(0, 1.05)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    # 新增繪圖：平均經驗卸載率
    fig, ax = plt.subplots(figsize=(8, 6))
    fig.suptitle(f"Fig. 4 Simulation: Mean Empirical Offloading Rate", fontsize=14)
    ax.bar(["Low-value", "High-value"], [offload_L_rate, offload_H_rate], color=['cornflowerblue', 'brown'])
    ax.set_title("Mean Empirical Offloading Rate")
    ax.set_ylabel("Offloading Rate")
    ax.set_ylim(0, 1.05)
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return results

# --- 新增: 模擬 Fig. 5: 意外記憶測試 ---
def simulate_fig5(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 5 (意外記憶測試)...")

    params_common = replace(base_params,
        n_high_items=3, n_low_items=3,
    )

    # 條件1: 不允許考慮卸載
    print("  Fig. 5 條件1: 不允許考慮卸載 (意外測試)")
    strategies_cond1 = get_all_strategies(allow_offloading=False)
    results_cond1 = run_simulation_original(params_common, strategies_cond1)

    # 條件2: 只允許考慮卸載高價值項目
    print("  Fig. 5 條件2: 只允許考慮卸載高價值 (意外測試)")
    strategies_cond2 = get_all_strategies(allow_offloading=True, offload_restriction="high_only")
    results_cond2 = run_simulation_original(params_common, strategies_cond2)

    # 匯總和打印結果 (提取意外準確率)
    acc_L_cond1_surprise = results_cond1["mean_accuracy_L_surprise"]
    acc_H_cond1_surprise = results_cond1["mean_accuracy_H_surprise"]
    acc_L_cond2_surprise = results_cond2["mean_accuracy_L_surprise"]
    acc_H_cond2_surprise = results_cond2["mean_accuracy_H_surprise"]

    print(f"    意外測試準確率 - 不允許考慮卸載 - 低價值: {acc_L_cond1_surprise:.3f}, 高價值: {acc_H_cond1_surprise:.3f}")
    print(f"    意外測試準確率 - 只允許考慮卸載高價值 - 低價值: {acc_L_cond2_surprise:.3f}, 高價值: {acc_H_cond2_surprise:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4), sharey=True)
    fig.suptitle(f"Fig. 5 Simulation: Surprise-Test Accuracy", fontsize=14)

    axs[0].bar(["Low-value", "High-value"], [acc_L_cond1_surprise, acc_H_cond1_surprise], color='cornflowerblue')
    axs[0].set_title("No Offloading Allowed")
    axs[0].set_ylabel("Mean Empirical Surprise-Test Accuracy")
    axs[0].set_ylim(0, 1.0)

    axs[1].bar(["Low-value", "High-value"], [acc_L_cond2_surprise, acc_H_cond2_surprise], color='lightcoral')
    axs[1].set_title("High-Value Offloading Allowed")
    axs[1].set_ylim(0, 1.0)

    plt.tight_layout(rect=[0, 0, 1, 0.94])
    plt.show()
    return {"cond1": results_cond1, "cond2": results_cond2}

# --- 新增: 模擬 Fig. 6: 記憶負荷對卸載率的影響 ---
def simulate_fig6(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 6 (記憶負荷對卸載率的影響)...")

    # 條件1: 每種價值1個項目 (1 high, 1 low)
    print("  Fig. 6 條件1: 每種價值1個項目 (記憶負荷=2)")
    params_load1 = replace(base_params,
        n_high_items=1, n_low_items=1,
    )
    strategies_load1 = get_all_strategies(allow_offloading=True)
    results_load1 = run_simulation_original(params_load1, strategies_load1)

    # 匯總和打印結果
    offload_L_rate_load1 = results_load1["mean_offload_L"] # 平均經驗卸載率
    offload_H_rate_load1 = results_load1["mean_offload_H"] # 平均經驗卸載率
    print(f"    平均經驗卸載率 (記憶負荷=2) - 低價值: {offload_L_rate_load1:.3f}, 高價值: {offload_H_rate_load1:.3f}")

    # 條件2: 每種價值3個項目 (標準情況)
    print("  Fig. 6 條件2: 每種價值3個項目 (標準負荷=6)")
    params_load3 = replace(base_params,
        n_high_items=3, n_low_items=3,
    )
    strategies_load3 = get_all_strategies(allow_offloading=True)
    results_load3 = run_simulation_original(params_load3, strategies_load3)

    # 匯總和打印結果
    offload_L_rate_load3 = results_load3["mean_offload_L"] # 平均經驗卸載率
    offload_H_rate_load3 = results_load3["mean_offload_H"] # 平均經驗卸載率
    print(f"    平均經驗卸載率 (記憶負荷=6) - 低價值: {offload_L_rate_load3:.3f}, 高價值: {offload_H_rate_load3:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(10, 4), sharey=True)
    fig.suptitle(f"Fig. 6 Simulation: Effect of Memory Load on Offloading Rate", fontsize=14)

    axs[0].bar(["Low-value", "High-value"], [offload_L_rate_load1, offload_H_rate_load1], color='cornflowerblue')
    axs[0].set_title("Memory Load = 2")
    axs[0].set_ylabel("Mean Empirical Offloading Rate")
    axs[0].set_ylim(0, 1.0)

    axs[1].bar(["Low-value", "High-value"], [offload_L_rate_load3, offload_H_rate_load3], color='lightcoral')
    axs[1].set_title("Memory Load = 6")
    axs[1].set_ylim(0, 1.0)

    plt.tight_layout(rect=[0, 0, 1, 0.94])
    plt.show()
    return {"load1": results_load1, "load3": results_load3}

# --- 模擬 Fig. 7: 卸載成本的影響 ---
def simulate_fig7(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 7 (卸載成本的影響)...")
    # 卸載成本從 0.0 到 2.0 分成 9 個點
    offloading_costs = np.linspace(0.0, 2.0, 9) 
    
    # 初始化結果存儲列表
    results_over_costs: Dict[str, List[Any]] = { # 使用 Any 來允許存儲不同類型的結果
        "costs": list(offloading_costs), 
        "memory_encoding_rate_low": [], "memory_encoding_rate_high": [],
        "offloading_rate_low": [], "offloading_rate_high": [],
        "mean_accuracy_H_normal": [], "mean_accuracy_L_normal": [], # 添加經驗準確率存儲
        "mean_accuracy_H_surprise": [], "mean_accuracy_L_surprise": [] # 添加經驗意外準確率存儲
    }
    
    # 所有策略 (允許所有卸載選項)
    strategies_fig7 = get_all_strategies(allow_offloading=True)

    # 針對每個卸載成本運行模擬
    for cost_idx, cost in enumerate(offloading_costs):
        print(f"  模擬卸載成本 ({cost_idx+1}/{len(offloading_costs)}): {cost:.2f}")
        
        # 為當前成本創建參數對象
        current_params = ModelParameters(
            n_high_items=3, n_low_items=3, 
            value_high=base_params.value_high, value_low=base_params.value_low,
            cost_offloading=cost, # 使用當前的卸載成本
            accuracy_internal_1_item=base_params.accuracy_internal_1_item,
            accuracy_internal_2_items=base_params.accuracy_internal_2_items,
            accuracy_internal_3_items=base_params.accuracy_internal_3_items,
            accuracy_internal_6_items=base_params.accuracy_internal_6_items,
            accuracy_offloaded=base_params.accuracy_offloaded,
            n_episodes_per_strategy_eval=base_params.n_episodes_per_strategy_eval, # 每個 run 的 episodes 數量
            n_model_runs=base_params.n_model_runs # 運行次數
        )
        
        # 使用 run_simulation 函數
        sim_results = run_simulation_original(current_params, strategies_fig7) # 移除 method 參數
        
        # 從結果中提取編碼率、卸載率和經驗準確率
        prop_encode_low = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[1])
        prop_encode_high = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[0])
        prop_offload_low = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[3])
        prop_offload_high = sum(prop for s, prop in sim_results["strategy_proportions"].items() if s[2])

        acc_H_norm = sim_results["mean_accuracy_H"] # 經驗正常準確率
        acc_L_norm = sim_results["mean_accuracy_L"] # 經驗正常準確率
        acc_H_surp = sim_results["mean_accuracy_H_surprise"] # 經驗意外準確率
        acc_L_surp = sim_results["mean_accuracy_L_surprise"] # 經驗意外準確率

        
        # 將結果添加到列表中
        results_over_costs["memory_encoding_rate_low"].append(prop_encode_low)
        results_over_costs["memory_encoding_rate_high"].append(prop_encode_high)
        results_over_costs["offloading_rate_low"].append(prop_offload_low)
        results_over_costs["offloading_rate_high"].append(prop_offload_high)
        results_over_costs["mean_accuracy_H_normal"].append(acc_H_norm)
        results_over_costs["mean_accuracy_L_normal"].append(acc_L_norm)
        results_over_costs["mean_accuracy_H_surprise"].append(acc_H_surp)
        results_over_costs["mean_accuracy_L_surprise"].append(acc_L_surp)

    print(f"Fig. 7 結果: ")
    for i, cost_val in enumerate(results_over_costs["costs"]):
        print(f"  成本={cost_val:.2f}: EncL={results_over_costs['memory_encoding_rate_low'][i]:.3f}, EncH={results_over_costs['memory_encoding_rate_high'][i]:.3f}, OffL={results_over_costs['offloading_rate_low'][i]:.3f}, OffH={results_over_costs['offloading_rate_high'][i]:.3f}, AccL_norm={results_over_costs['mean_accuracy_L_normal'][i]:.3f}, AccH_norm={results_over_costs['mean_accuracy_H_normal'][i]:.3f}, AccL_surp={results_over_costs['mean_accuracy_L_surprise'][i]:.3f}, AccH_surp={results_over_costs['mean_accuracy_H_surprise'][i]:.3f}")


    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 7 Simulation: Effect of Offloading Cost", fontsize=14)
    axs[0].plot(results_over_costs["costs"], results_over_costs["memory_encoding_rate_low"], 'o-', label="Low-value", color='cornflowerblue')
    axs[0].plot(results_over_costs["costs"], results_over_costs["memory_encoding_rate_high"], 's-', label="High-value", color='brown')
    axs[0].set_title("Memory encoding")
    axs[0].set_xlabel("Cost of offloading")
    axs[0].set_ylabel("Memory encoding rate")
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    # 在右側子圖繪製卸載率和準確率
    # 移除 ax2 的創建和繪製準確率的部分
    # ax2 = axs[1].twinx() # 創建一個共用 x 軸的雙軸

    # 繪製卸載率 (使用左軸)
    axs[1].plot(results_over_costs["costs"], results_over_costs["offloading_rate_low"], 'o--', label="Offload Low", color='cornflowerblue')
    axs[1].plot(results_over_costs["costs"], results_over_costs["offloading_rate_high"], 's--', label="Offload High", color='brown')
    axs[1].set_ylabel("Offloading rate")
    axs[1].set_ylim(0, 1.05)


    axs[1].set_title("Offloading Rate") 
    axs[1].set_xlabel("Cost of offloading")
    # 合併圖例 - 移除合併，只保留 axs[1] 的圖例
    # lines, labels = axs[1].get_legend_handles_labels()
    # lines2, labels2 = ax2.get_legend_handles_labels()
    # ax2.legend(lines + lines2, labels + labels2, loc='best')
    axs[1].legend(loc='best') # 只顯示 axs[1] 的圖例


    axs[1].grid(True, linestyle='--', alpha=0.7) # 網格線
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

# Fig. 8 專用的 run_simulation 函數 
def run_simulation_fig8_original(params: ModelParameters, internal_accuracy_scalar: float) -> Dict[str, float]:
    """
    運行 Fig. 8 原始蒙地卡羅模擬評估策略 (單一項目，不包含後設認知)
    """
    strategies_fig8 = get_strategies_fig8()

    overall_best_strategy_counts: Dict[StrategyFig8, int] = {s: 0 for s in strategies_fig8}
    cumulative_best_strategy_total_offloaded = 0
    cumulative_best_strategy_total_episodes = 0
    successful_runs_count = 0

    for i_run in range(params.n_model_runs):
        if (i_run + 1) % (max(1, params.n_model_runs // min(100, params.n_model_runs))) == 0:
            print(f"  運行中 Fig 8... {((i_run + 1) / params.n_model_runs) * 100:.0f}%")

        total_reward_per_strategy_this_run: Dict[StrategyFig8, float] = defaultdict(float)
        sample_count_per_strategy_this_run: Dict[StrategyFig8, int] = defaultdict(int)
        total_offloaded_per_strategy_this_run: Dict[StrategyFig8, int] = defaultdict(int)

        allowed_strategies_list_this_run = strategies_fig8

        for i_episode in range(params.n_episodes_per_strategy_eval):
            chosen_strategy = random.choice(allowed_strategies_list_this_run)
            chosen_strategy_tuple = tuple(chosen_strategy)

            # 使用原始的 simulate_trial_fig8 (不包含後設認知)
            reward, _, _, actual_offloaded = simulate_trial_fig8_original(params, chosen_strategy_tuple, internal_accuracy_scalar)

            total_reward_per_strategy_this_run[chosen_strategy_tuple] += reward
            sample_count_per_strategy_this_run[chosen_strategy_tuple] += 1
            total_offloaded_per_strategy_this_run[chosen_strategy_tuple] += actual_offloaded

        mean_rewards_this_run: Dict[StrategyFig8, float] = {}
        max_avg_reward_this_run = -float('inf')
        strategies_sampled_this_run = [s for s in strategies_fig8 if sample_count_per_strategy_this_run[s] > 0]

        if not strategies_sampled_this_run:
            continue

        for strategy in strategies_sampled_this_run:
            if sample_count_per_strategy_this_run[strategy] > 0:
                mean_reward = total_reward_per_strategy_this_run[strategy] / sample_count_per_strategy_this_run[strategy]
                mean_rewards_this_run[strategy] = mean_reward
                if mean_reward > max_avg_reward_this_run:
                    max_avg_reward_this_run = mean_reward

        tolerance = 1e-9
        best_strategies_this_run = [
            s for s, avg_r in mean_rewards_this_run.items() if abs(avg_r - max_avg_reward_this_run) < tolerance
        ]

        best_strategy_for_this_run = random.choice(best_strategies_this_run)
        overall_best_strategy_counts[best_strategy_for_this_run] += 1

        samples_best_strategy_this_run = sample_count_per_strategy_this_run[best_strategy_for_this_run]

        if samples_best_strategy_this_run > 0:
            cumulative_best_strategy_total_offloaded += total_offloaded_per_strategy_this_run[best_strategy_for_this_run]
            cumulative_best_strategy_total_episodes += samples_best_strategy_this_run
            successful_runs_count += 1

    total_runs_considered = successful_runs_count

    prop_encode_chosen = sum(count for (encode, offload_allowed), count in overall_best_strategy_counts.items() if encode) / total_runs_considered if total_runs_considered > 0 else 0.0
    prop_offload_allowed_chosen = sum(count for (encode, offload_allowed), count in overall_best_strategy_counts.items() if offload_allowed) / total_runs_considered if total_runs_considered > 0 else 0.0

    total_item_opportunities = cumulative_best_strategy_total_episodes * params.n_high_items

    mean_empirical_offload_rate = cumulative_best_strategy_total_offloaded / total_item_opportunities if total_item_opportunities > 0 else 0.0

    return {
        "prop_encode_chosen": prop_encode_chosen,
        "prop_offload_allowed_chosen": prop_offload_allowed_chosen,
        "mean_offload_rate": mean_empirical_offload_rate
    }

# Fig. 8 專用的 simulate_trial (原始版本)
def simulate_trial_fig8_original(params: ModelParameters, strategy: StrategyFig8, internal_accuracy_scalar: float) -> Tuple[float, bool, bool, int]:
    """
    模擬單次試驗並計算總獎勵和命中數 (Fig. 8 原始版本，不包含後設認知)
    """
    encode_strategy = strategy[0]
    offload_allowed = strategy[1]

    total_reward = 0.0
    item_recalled_normal = False
    item_recalled_surprise = False
    actual_offloaded_count = 0

    if encode_strategy and offload_allowed:
        # 同時使用內部記憶和卸載
        actual_offloaded_count = 1
        combined_success_prob = 1.0 - (1.0 - internal_accuracy_scalar) * (1.0 - params.accuracy_offloaded)
        if np.random.rand() < combined_success_prob:
            item_recalled_normal = True
        if np.random.rand() < internal_accuracy_scalar:
            item_recalled_surprise = True
    elif encode_strategy:
        # 只使用內部記憶
        if np.random.rand() < internal_accuracy_scalar:
            item_recalled_normal = True
            item_recalled_surprise = True
    elif offload_allowed:
        # 只使用卸載
        actual_offloaded_count = 1
        if np.random.rand() < params.accuracy_offloaded:
            item_recalled_normal = True

    if item_recalled_normal:
        total_reward += params.value_high

    # 計算卸載成本
    if actual_offloaded_count > 0:
        total_reward -= actual_offloaded_count * params.cost_offloading

    return total_reward, item_recalled_normal, item_recalled_surprise, actual_offloaded_count

# 修改 simulate_fig8 函數以使用原始版本
def simulate_fig8(base_params: ModelParameters):
    print(f"\n開始模擬 Fig. 8 (內部記憶準確率的影響)...")

    # Fig. 8 變化的是內部記憶準確率
    internal_accuracies_to_test = np.linspace(0.55, 0.95, 9)

    # 初始化結果存儲列表
    low_cost_offload_rates = []
    high_cost_offload_rates = []
    internal_acc_values_recorded = []

    # Fig 8 模擬的是單一高價值項目
    params_base_fig8 = replace(base_params,
        n_high_items=1, n_low_items=0,
        value_high=base_params.value_high, value_low=0,
    )

    # 遍歷不同的內部記憶準確率
    for int_acc in internal_accuracies_to_test:
        print(f"  模擬內部記憶準確率: {int_acc:.2f}")

        # 低卸載成本條件
        params_low_cost_fig8 = replace(params_base_fig8,
            cost_offloading=1.0
        )
        results_low_cost = run_simulation_fig8_original(params_low_cost_fig8, int_acc)
        low_cost_offload_rates.append(results_low_cost['mean_offload_rate'])


        # 高卸載成本條件
        params_high_cost_fig8 = replace(params_base_fig8,
            cost_offloading=2.0
        )
        results_high_cost = run_simulation_fig8_original(params_high_cost_fig8, int_acc)
        high_cost_offload_rates.append(results_high_cost['mean_offload_rate'])

        # 記錄當前使用的 internal accuracy 值
        internal_acc_values_recorded.append(int_acc)

    # 計算成本敏感度
    cost_sensitivity = np.array(low_cost_offload_rates) - np.array(high_cost_offload_rates)

    print(f"Fig. 8 結果: ")
    for i, int_acc_val in enumerate(internal_acc_values_recorded):
        print(f"  內部準確率={int_acc_val:.2f}: 低成本卸載率={low_cost_offload_rates[i]:.3f}, 高成本卸載率={high_cost_offload_rates[i]:.3f}, 成本敏感度={cost_sensitivity[i]:.3f}")

    # 繪圖
    fig, axs = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f"Fig. 8 Simulation: Effect of Internal Accuracy", fontsize=14)

    # 左圖：卸載率 vs 內部記憶準確率
    axs[0].plot(internal_acc_values_recorded, low_cost_offload_rates, 'o-', label="Low Cost (cost=1)", color='blue')
    axs[0].plot(internal_acc_values_recorded, high_cost_offload_rates, 's-', label="High Cost (cost=2)", color='darkred')
    axs[0].set_title("Mean Empirical Offloading Rate")
    axs[0].set_xlabel("Internal Accuracy")
    axs[0].set_ylabel("Mean Empirical Offloading Rate")
    axs[0].set_ylim(0, 1.05)
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    # 右圖：成本敏感度 vs 內部記憶準確率
    axs[1].plot(internal_acc_values_recorded, cost_sensitivity, '^-', label="Cost Sensitivity", color='green')
    axs[1].set_title("Cost Sensitivity")
    axs[1].set_xlabel("Internal Accuracy")
    axs[1].set_ylabel("Cost Sensitivity (Offload Rate Diff)")
    y_min_sens = min(0, min(cost_sensitivity) * 1.2 if cost_sensitivity.size > 0 else 0)
    y_max_sens = max(0.55, max(cost_sensitivity) * 1.2 if cost_sensitivity.size > 0 else 0.55)
    axs[1].set_ylim(y_min_sens, y_max_sens)

    axs[1].legend()
    axs[1].grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return {"internal_accuracies": internal_acc_values_recorded, "low_cost_offload": low_cost_offload_rates, "high_cost_offload": high_cost_offload_rates, "cost_sensitivity": high_cost_offload_rates}

# --- 新增: 模擬 後設認知閾值的影響 ---
def simulate_metacognition_effect(base_params: ModelParameters):
    print(f"\n開始模擬 後設認知閾值的影響...")

    # 定義要測試的後設認知閾值範圍
    # 閾值適用於內部記憶準確率 (0 到 1)。從 0.0 到 1.0 測試一些點。
    metacognitive_thresholds = np.linspace(0.0, 1.0, 11) # 例如: 0.0, 0.1, ..., 1.0

    # 初始化結果存储列表
    results_over_thresholds: Dict[str, List[Any]] = {
        "thresholds": list(metacognitive_thresholds),
        "mean_offload_H": [], # 存储平均經驗卸載率
        "mean_offload_L": [], # 存储平均經驗卸載率
        "mean_accuracy_H_normal": [],
        "mean_accuracy_L_normal": [],
        "mean_accuracy_H_surprise": [],
        "mean_accuracy_L_surprise": []
    }

    # 使用標準策略集 (允許對高低價值項目都考慮卸載) 來進行測試
    strategies_to_test = get_all_strategies(allow_offloading=True)

    # 遍歷不同的後設認知閾值
    for threshold_idx, threshold in enumerate(metacognitive_thresholds):
        print(f"  模擬後設認知閾值 ({threshold_idx+1}/{len(metacognitive_thresholds)}): {threshold:.2f}")

        # 為當前閾值創建參數對象 (使用 base_params 的其餘參數)
        current_params = replace(base_params,
            metacognitive_threshold=threshold # 設定當前的後設認知閾值
            # 其他參數從 base_params 複製
        )

        # 運行模擬 (使用新的 run_simulation_with_metacognition 函數)
        sim_results = run_simulation_with_metacognition(current_params, strategies_to_test)

        # 存儲相關結果 (特別是平均經驗卸載率和準確率)
        results_over_thresholds["mean_offload_H"].append(sim_results["mean_offload_H"])
        results_over_thresholds["mean_offload_L"].append(sim_results["mean_offload_L"])
        results_over_thresholds["mean_accuracy_H_normal"].append(sim_results["mean_accuracy_H"])
        results_over_thresholds["mean_accuracy_L_normal"].append(sim_results["mean_accuracy_L"])
        results_over_thresholds["mean_accuracy_H_surprise"].append(sim_results["mean_accuracy_H_surprise"])
        results_over_thresholds["mean_accuracy_L_surprise"].append(sim_results["mean_accuracy_L_surprise"])

    # 打印结果
    print(f"\n後設認知閾值影響模擬結果:")
    for i, thresh_val in enumerate(results_over_thresholds["thresholds"]):
        print(f"  閾值={thresh_val:.2f}: OffH={results_over_thresholds['mean_offload_H'][i]:.3f}, OffL={results_over_thresholds['mean_offload_L'][i]:.3f}, AccH_norm={results_over_thresholds['mean_accuracy_H_normal'][i]:.3f}, AccL_norm={results_over_thresholds['mean_accuracy_L_normal'][i]:.3f}, AccH_surp={results_over_thresholds['mean_accuracy_H_surprise'][i]:.3f}, AccL_surp={results_over_thresholds['mean_accuracy_L_surprise'][i]:.3f}")

    # 繪圖 (平均經驗卸載率 vs. 後設認知閾值)
    fig, ax = plt.subplots(figsize=(10, 6))
    fig.suptitle(f"Mean Empirical Offloading Rate vs. Metacognitive Threshold", fontsize=14)

    # 使用紅色線條和圓形標記繪製高價值項目
    ax.plot(results_over_thresholds["thresholds"], results_over_thresholds["mean_offload_H"], 'o-', label="High-value items", color='darkred')
    # 使用藍色線條和方形標記繪製低價值項目
    ax.plot(results_over_thresholds["thresholds"], results_over_thresholds["mean_offload_L"], 's-', label="Low-value items", color='royalblue')

    ax.set_xlabel("Metacognitive Threshold (Perceived Internal Accuracy)")
    ax.set_ylabel("Mean Empirical Offloading Rate")
    ax.set_ylim(0, 1.05)
    ax.legend()
    ax.grid(True, linestyle='--', alpha=0.7)

    # 確保圖表顯示完整
   
    plt.tight_layout()
    plt.show()

    # 可以選擇也繪製準確率隨閾值變化的圖表，如果需要的話
    fig, axs = plt.subplots(1, 2, figsize=(10, 4), sharey=True)
    fig.suptitle(f"Effect of Metacognitive Threshold on Mean Empirical Accuracy", fontsize=14)
    axs[0].plot(results_over_thresholds["thresholds"], results_over_thresholds["mean_accuracy_H_normal"], 'o-', label="High-value Normal", color='brown')
    axs[0].plot(results_over_thresholds["thresholds"], results_over_thresholds["mean_accuracy_L_normal"], 's-', label="Low-value Normal", color='cornflowerblue')
    axs[0].set_title("Normal Accuracy")
    axs[0].set_xlabel("Metacognitive Threshold")
    axs[0].set_ylabel("Mean Empirical Accuracy")
    axs[0].set_ylim(0, 1.05)
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    axs[1].plot(results_over_thresholds["thresholds"], results_over_thresholds["mean_accuracy_H_surprise"], 'o--', label="High-value Surprise", color='brown')
    axs[1].plot(results_over_thresholds["thresholds"], results_over_thresholds["mean_accuracy_L_surprise"], 's--', label="Low-value Surprise", color='cornflowerblue')
    axs[1].set_title("Surprise Accuracy")
    axs[1].set_xlabel("Metacognitive Threshold")
    axs[1].set_ylim(0, 1.05)
    axs[1].legend()
    axs[1].grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return results_over_thresholds


# --- 新增: 模擬後設認知偏差的影響 (基於 Hu, Luo, & Fleming 2019) ---
def simulate_metacognitive_bias_effect(base_params: ModelParameters):
    """
    模擬後設認知偏差對策略選擇和卸載行為的影響。
    基於 Hu, Luo, & Fleming (2019) 的理論，探討不同程度的後設認知偏差
    如何影響個體對內部記憶的主觀評估，進而影響卸載決策。

    Args:
        base_params: 基礎模型參數

    Returns:
        不同後設認知偏差下的模擬結果
    """
    print(f"\n開始模擬後設認知偏差的影響 (基於 Hu, Luo, & Fleming 2019)...")

    # 定義要測試的後設認知偏差範圍
    # 負值表示低估內部記憶能力，正值表示高估內部記憶能力
    metacognitive_biases = np.linspace(-0.3, 0.3, 7)  # 例如: -0.3, -0.2, -0.1, 0, 0.1, 0.2, 0.3

    # 初始化結果存儲列表
    results_over_biases: Dict[str, List[Any]] = {
        "biases": list(metacognitive_biases),
        "mean_offload_H": [],  # 存儲平均經驗卸載率
        "mean_offload_L": [],  # 存儲平均經驗卸載率
        "mean_accuracy_H_normal": [],
        "mean_accuracy_L_normal": [],
        "mean_accuracy_H_surprise": [],
        "mean_accuracy_L_surprise": []
    }

    # 使用標準策略集進行測試
    strategies_to_test = get_all_strategies(allow_offloading=True)

    # 遍歷不同的後設認知偏差
    for bias_idx, bias in enumerate(metacognitive_biases):
        print(f"  模擬後設認知偏差 ({bias_idx+1}/{len(metacognitive_biases)}): {bias:.2f}")

        # 為當前偏差創建參數對象
        current_params = replace(base_params,
            metacognitive_bias=bias  # 設定當前的後設認知偏差
            # 其他參數從 base_params 複製
        )

        # 運行模擬 (使用新的 run_simulation_with_metacognition 函數)
        sim_results = run_simulation_with_metacognition(current_params, strategies_to_test)

        # 存儲相關結果
        results_over_biases["mean_offload_H"].append(sim_results["mean_offload_H"])
        results_over_biases["mean_offload_L"].append(sim_results["mean_offload_L"])
        results_over_biases["mean_accuracy_H_normal"].append(sim_results["mean_accuracy_H"])
        results_over_biases["mean_accuracy_L_normal"].append(sim_results["mean_accuracy_L"])
        results_over_biases["mean_accuracy_H_surprise"].append(sim_results["mean_accuracy_H_surprise"])
        results_over_biases["mean_accuracy_L_surprise"].append(sim_results["mean_accuracy_L_surprise"])

    # 打印結果
    print(f"\n後設認知偏差影響模擬結果:")
    for i, bias_val in enumerate(results_over_biases["biases"]):
        print(f"  偏差={bias_val:.2f}: OffH={results_over_biases['mean_offload_H'][i]:.3f}, OffL={results_over_biases['mean_offload_L'][i]:.3f}, AccH_norm={results_over_biases['mean_accuracy_H_normal'][i]:.3f}, AccL_norm={results_over_biases['mean_accuracy_L_normal'][i]:.3f}, AccH_surp={results_over_biases['mean_accuracy_H_surprise'][i]:.3f}, AccL_surp={results_over_biases['mean_accuracy_L_surprise'][i]:.3f}")

    # 繪图 (後設認知偏差 vs. 卸載率)
    fig, ax = plt.subplots(figsize=(10, 6))
    fig.suptitle(f"Effect of Metacognitive Bias on Mean Empirical Offloading Rate", fontsize=14)

    # 使用紅色線條和圓形標記繪製高價值項目
    ax.plot(results_over_biases["biases"], results_over_biases["mean_offload_H"], 'o-', label="High-value items", color='darkred')
    # 使用藍色線條和方形標記繪製低價值項目
    ax.plot(results_over_biases["biases"], results_over_biases["mean_offload_L"], 's-', label="Low-value items", color='royalblue')

    ax.set_xlabel("Metacognitive Bias\n(Negative = Underconfidence, Positive = Overconfidence)")
    ax.set_ylabel("Mean Empirical Offloading Rate")
    ax.set_ylim(0, 1.05)
    ax.axvline(x=0, color='gray', linestyle='--', alpha=0.7)  # 添加垂直線表示無偏差
    ax.legend()
    ax.grid(True, linestyle='--', alpha=0.7)

    # 確保圖表顯示完整
    plt.tight_layout()
    plt.show()

    # 繪製準確率隨後設認知偏差變化的圖表
    fig, axs = plt.subplots(1, 2, figsize=(12, 5), sharey=True)
    fig.suptitle(f"Effect of Metacognitive Bias on Mean Empirical Accuracy", fontsize=14)

    # 正常準確率
    axs[0].plot(results_over_biases["biases"], results_over_biases["mean_accuracy_H_normal"], 'o-', label="High-value Normal", color='darkred')
    axs[0].plot(results_over_biases["biases"], results_over_biases["mean_accuracy_L_normal"], 's-', label="Low-value Normal", color='cornflowerblue')
    axs[0].set_title("Normal Accuracy")
    axs[0].set_xlabel("Metacognitive Bias")
    axs[0].set_ylabel("Mean Empirical Accuracy")
    axs[0].set_ylim(0, 1.05)
    axs[0].axvline(x=0, color='gray', linestyle='--', alpha=0.7)
    axs[0].legend()
    axs[0].grid(True, linestyle='--', alpha=0.7)

    # 意外準確率
    axs[1].plot(results_over_biases["biases"], results_over_biases["mean_accuracy_H_surprise"], 'o--', label="High-value Surprise", color='darkred')
    axs[1].plot(results_over_biases["biases"], results_over_biases["mean_accuracy_L_surprise"], 's--', label="Low-value Surprise", color='cornflowerblue')
    axs[1].set_title("Surprise Accuracy")
    axs[1].set_xlabel("Metacognitive Bias")
    axs[1].set_ylim(0, 1.05)
    axs[1].axvline(x=0, color='gray', linestyle='--', alpha=0.7)
    axs[1].legend()
    axs[1].grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.show()

    return results_over_biases


if __name__ == '__main__':
    shared_base_params = ModelParameters(
        n_model_runs=100,  
        n_episodes_per_strategy_eval=20,  
        metacognitive_threshold=0.85  # 只用於後設認知模擬 - 設為合理值
    )
    print(f"--- 執行所有圖表模擬 (Fig 2-8 使用原始方法，後設認知影響單獨測試) ---")

    # 執行 Fig 2-8 模擬 (使用原始方法，不包含後設認知)
    results_fig2 = simulate_fig2(shared_base_params)
    results_fig3 = simulate_fig3(shared_base_params)
    results_fig4 = simulate_fig4(shared_base_params)

    # 比較低價值準確率
    if results_fig2 and results_fig4:
        print(f"\n  比較低價值正常準確率: Fig.2 (不卸載)={results_fig2['mean_accuracy_L']:.3f} vs Fig.4 (僅允許卸載高價值)={results_fig4['mean_accuracy_L']:.3f}")

    results_fig5 = simulate_fig5(shared_base_params)
    results_fig6 = simulate_fig6(shared_base_params)
    results_fig7 = simulate_fig7(shared_base_params)
    results_fig8 = simulate_fig8(shared_base_params)

    # 單独測試後設認知的影響
    results_metacognition = simulate_metacognition_effect(shared_base_params)

    # 模擬後設認知偏差的影響 (基於 Hu, Luo, & Fleming 2019)
    print(f"\n開始模擬後設認知偏差的影響 (基於 Hu, Luo, & Fleming 2019)...")
    simulate_metacognitive_bias_effect(shared_base_params)

    print(f"\n所有模擬完成！")
